<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理 - 电气元器件管理系统</title>
    <link rel="stylesheet" href="lib/element-ui.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-box {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 400px;
            position: relative;
        }
        .login-header {
            position: relative;
        }
        .back-to-main {
            position: absolute;
            top: 0;
            right: 0;
            color: #666 !important;
            font-size: 14px;
            padding: 5px 10px;
        }
        .back-to-main:hover {
            color: #409EFF !important;
            background-color: #f5f7fa;
        }
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        .header-buttons {
            display: flex;
            gap: 10px;
        }
        .table-container {
            margin-top: 20px;
        }
        .search-bar {
            margin-bottom: 15px;
        }
        .pagination-container {
            margin-top: 20px;
            text-align: center;
        }
        .edit-dialog .el-form-item {
            margin-bottom: 20px;
        }
        .tab-content {
            min-height: 600px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 登录界面 -->
        <div v-if="!isAuthenticated" class="login-container">
            <div class="login-box">
                <div class="login-header">
                    <h2 style="text-align: center; margin-bottom: 30px; color: #333;">数据库管理</h2>
                    <el-button
                        @click="goToMainPage"
                        type="text"
                        icon="el-icon-back"
                        class="back-to-main">
                        返回主页
                    </el-button>
                </div>
                <el-form @submit.native.prevent="login">
                    <el-form-item>
                        <el-input
                            v-model="loginPassword"
                            type="password"
                            placeholder="请输入管理密码"
                            show-password
                            @keyup.enter.native="login">
                        </el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="login"
                            :loading="loginLoading"
                            style="width: 100%;">
                            登录
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <!-- 管理界面 -->
        <div v-if="isAuthenticated" class="admin-container">
            <div class="admin-header">
                <h1>数据库管理</h1>
                <div class="header-buttons">
                    <el-button @click="goToMainPage" type="primary" plain icon="el-icon-back">返回主页</el-button>
                    <el-button @click="logout" type="danger" plain icon="el-icon-switch-button">退出登录</el-button>
                </div>
            </div>

            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
                <!-- 制造商管理 -->
                <el-tab-pane label="制造商管理" name="manufacturers">
                    <div class="tab-content">
                        <div class="search-bar">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-input
                                        v-model="manufacturerSearch"
                                        placeholder="搜索制造商名称或描述"
                                        @keyup.enter.native="loadManufacturers"
                                        clearable>
                                        <el-button slot="append" icon="el-icon-search" @click="loadManufacturers"></el-button>
                                    </el-input>
                                </el-col>
                                <el-col :span="6">
                                    <el-button type="primary" @click="showAddManufacturerDialog">添加制造商</el-button>
                                </el-col>
                                <el-col :span="6">
                                    <el-button
                                        type="danger"
                                        :disabled="selectedManufacturers.length === 0"
                                        @click="batchDeleteManufacturers">
                                        批量删除 ({{selectedManufacturers.length}})
                                    </el-button>
                                </el-col>
                            </el-row>
                        </div>

                        <div class="table-container">
                            <el-table
                                :data="manufacturers"
                                v-loading="manufacturerLoading"
                                border
                                stripe
                                @selection-change="handleManufacturerSelectionChange">
                                <el-table-column type="selection" width="55"></el-table-column>
                                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                                <el-table-column prop="name" label="名称" width="200"></el-table-column>
                                <el-table-column prop="description" label="描述"></el-table-column>
                                <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
                                <el-table-column label="操作" width="150">
                                    <template slot-scope="scope">
                                        <el-button size="mini" @click="editManufacturer(scope.row)">编辑</el-button>
                                        <el-button size="mini" type="danger" @click="deleteManufacturer(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <div class="pagination-container">
                            <el-pagination
                                @size-change="handleManufacturerSizeChange"
                                @current-change="handleManufacturerCurrentChange"
                                :current-page="manufacturerPage"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="manufacturerPageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="manufacturerTotal">
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>

                <!-- 产品组管理 -->
                <el-tab-pane label="产品组管理" name="product_groups">
                    <div class="tab-content">
                        <div class="search-bar">
                            <el-row :gutter="20">
                                <el-col :span="5">
                                    <el-select v-model="productGroupManufacturerFilter" placeholder="选择制造商" clearable @change="loadProductGroups">
                                        <el-option
                                            v-for="mfg in allManufacturers"
                                            :key="mfg.id"
                                            :label="mfg.name"
                                            :value="mfg.id">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="9">
                                    <el-input
                                        v-model="productGroupSearch"
                                        placeholder="搜索产品组名称或描述"
                                        @keyup.enter.native="loadProductGroups"
                                        clearable>
                                        <el-button slot="append" icon="el-icon-search" @click="loadProductGroups"></el-button>
                                    </el-input>
                                </el-col>
                                <el-col :span="5">
                                    <el-button type="primary" @click="showAddProductGroupDialog">添加产品组</el-button>
                                </el-col>
                                <el-col :span="5">
                                    <el-button
                                        type="danger"
                                        :disabled="selectedProductGroups.length === 0"
                                        @click="batchDeleteProductGroups">
                                        批量删除 ({{selectedProductGroups.length}})
                                    </el-button>
                                </el-col>
                            </el-row>
                        </div>

                        <div class="table-container">
                            <el-table
                                :data="productGroups"
                                v-loading="productGroupLoading"
                                border
                                stripe
                                @selection-change="handleProductGroupSelectionChange">
                                <el-table-column type="selection" width="55"></el-table-column>
                                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                                <el-table-column prop="manufacturer_name" label="制造商" width="150"></el-table-column>
                                <el-table-column prop="name" label="产品组名称" width="200"></el-table-column>
                                <el-table-column prop="description" label="描述"></el-table-column>
                                <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
                                <el-table-column label="操作" width="150">
                                    <template slot-scope="scope">
                                        <el-button size="mini" @click="editProductGroup(scope.row)">编辑</el-button>
                                        <el-button size="mini" type="danger" @click="deleteProductGroup(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <div class="pagination-container">
                            <el-pagination
                                @size-change="handleProductGroupSizeChange"
                                @current-change="handleProductGroupCurrentChange"
                                :current-page="productGroupPage"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="productGroupPageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="productGroupTotal">
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>

                <!-- 子产品组管理 -->
                <el-tab-pane label="子产品组管理" name="sub_product_groups">
                    <div class="tab-content">
                        <div class="search-bar">
                            <el-row :gutter="20">
                                <el-col :span="5">
                                    <el-select v-model="subProductGroupManufacturerFilter" placeholder="选择制造商" clearable @change="onSubProductGroupManufacturerChange">
                                        <el-option
                                            v-for="mfg in allManufacturers"
                                            :key="mfg.id"
                                            :label="mfg.name"
                                            :value="mfg.id">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="5">
                                    <el-select v-model="subProductGroupProductGroupFilter" placeholder="选择产品组" clearable @change="loadSubProductGroups">
                                        <el-option
                                            v-for="pg in filteredProductGroups"
                                            :key="pg.id"
                                            :label="pg.name"
                                            :value="pg.id">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="8">
                                    <el-input 
                                        v-model="subProductGroupSearch" 
                                        placeholder="搜索子产品组名称或描述"
                                        @keyup.enter.native="loadSubProductGroups"
                                        clearable>
                                        <el-button slot="append" icon="el-icon-search" @click="loadSubProductGroups"></el-button>
                                    </el-input>
                                </el-col>
                                <el-col :span="4">
                                    <el-button type="primary" @click="showAddSubProductGroupDialog">添加子产品组</el-button>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20" style="margin-top: 10px;">
                                <el-col :span="4">
                                    <el-button
                                        type="danger"
                                        :disabled="selectedSubProductGroups.length === 0"
                                        @click="batchDeleteSubProductGroups">
                                        批量删除 ({{selectedSubProductGroups.length}})
                                    </el-button>
                                </el-col>
                            </el-row>
                        </div>

                        <div class="table-container">
                            <el-table
                                :data="subProductGroups"
                                v-loading="subProductGroupLoading"
                                border
                                stripe
                                @selection-change="handleSubProductGroupSelectionChange">
                                <el-table-column type="selection" width="55"></el-table-column>
                                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                                <el-table-column prop="manufacturer_name" label="制造商" width="120"></el-table-column>
                                <el-table-column prop="product_group_name" label="产品组" width="150"></el-table-column>
                                <el-table-column prop="name" label="子产品组名称" width="180"></el-table-column>
                                <el-table-column prop="description" label="描述"></el-table-column>
                                <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
                                <el-table-column label="操作" width="150">
                                    <template slot-scope="scope">
                                        <el-button size="mini" @click="editSubProductGroup(scope.row)">编辑</el-button>
                                        <el-button size="mini" type="danger" @click="deleteSubProductGroup(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <div class="pagination-container">
                            <el-pagination
                                @size-change="handleSubProductGroupSizeChange"
                                @current-change="handleSubProductGroupCurrentChange"
                                :current-page="subProductGroupPage"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="subProductGroupPageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="subProductGroupTotal">
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>

                <!-- 元器件管理 -->
                <el-tab-pane label="元器件管理" name="components">
                    <div class="tab-content">
                        <div class="search-bar">
                            <el-row :gutter="20">
                                <el-col :span="4">
                                    <el-select v-model="componentManufacturerFilter" placeholder="选择制造商" clearable @change="onComponentManufacturerChange">
                                        <el-option
                                            v-for="mfg in allManufacturers"
                                            :key="mfg.id"
                                            :label="mfg.name"
                                            :value="mfg.id">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="4">
                                    <el-select v-model="componentProductGroupFilter" placeholder="选择产品组" clearable @change="onComponentProductGroupChange">
                                        <el-option
                                            v-for="pg in filteredProductGroupsForComponent"
                                            :key="pg.id"
                                            :label="pg.name"
                                            :value="pg.id">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="4">
                                    <el-select v-model="componentSubProductGroupFilter" placeholder="选择子产品组" clearable @change="loadComponents">
                                        <el-option
                                            v-for="spg in filteredSubProductGroupsForComponent"
                                            :key="spg.id"
                                            :label="spg.name"
                                            :value="spg.id">
                                        </el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="8">
                                    <el-input 
                                        v-model="componentSearch" 
                                        placeholder="搜索型号、描述或备注"
                                        @keyup.enter.native="loadComponents"
                                        clearable>
                                        <el-button slot="append" icon="el-icon-search" @click="loadComponents"></el-button>
                                    </el-input>
                                </el-col>
                                <el-col :span="4">
                                    <el-button type="primary" @click="showAddComponentDialog">添加元器件</el-button>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20" style="margin-top: 10px;">
                                <el-col :span="4">
                                    <el-button
                                        type="danger"
                                        :disabled="selectedComponents.length === 0"
                                        @click="batchDeleteComponents">
                                        批量删除 ({{selectedComponents.length}})
                                    </el-button>
                                </el-col>
                            </el-row>
                        </div>

                        <div class="table-container">
                            <el-table
                                :data="components"
                                v-loading="componentLoading"
                                border
                                stripe
                                @selection-change="handleComponentSelectionChange">
                                <el-table-column type="selection" width="55"></el-table-column>
                                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                                <el-table-column prop="manufacturer_name" label="制造商" width="100"></el-table-column>
                                <el-table-column prop="product_group_name" label="产品组" width="120"></el-table-column>
                                <el-table-column prop="sub_product_group_name" label="子产品组" width="120"></el-table-column>
                                <el-table-column prop="model" label="型号" width="150"></el-table-column>
                                <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="notes" label="备注" show-overflow-tooltip></el-table-column>
                                <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
                                <el-table-column label="操作" width="150">
                                    <template slot-scope="scope">
                                        <el-button size="mini" @click="editComponent(scope.row)">编辑</el-button>
                                        <el-button size="mini" type="danger" @click="deleteComponent(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <div class="pagination-container">
                            <el-pagination
                                @size-change="handleComponentSizeChange"
                                @current-change="handleComponentCurrentChange"
                                :current-page="componentPage"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="componentPageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="componentTotal">
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>

        <!-- 编辑对话框 -->
        <el-dialog :title="editDialogTitle" :visible.sync="editDialogVisible" width="600px" class="edit-dialog">
            <el-form :model="editForm" :rules="editFormRules" ref="editForm" label-width="120px">
                <!-- 制造商编辑 -->
                <template v-if="editDialogType === 'manufacturer'">
                    <el-form-item label="制造商名称" prop="name">
                        <el-input v-model="editForm.name" placeholder="请输入制造商名称"></el-input>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="editForm.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
                    </el-form-item>
                </template>

                <!-- 产品组编辑 -->
                <template v-if="editDialogType === 'product_group'">
                    <el-form-item label="制造商" prop="manufacturer_id">
                        <el-select v-model="editForm.manufacturer_id" placeholder="请选择制造商" style="width: 100%;">
                            <el-option
                                v-for="mfg in allManufacturers"
                                :key="mfg.id"
                                :label="mfg.name"
                                :value="mfg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="产品组名称" prop="name">
                        <el-input v-model="editForm.name" placeholder="请输入产品组名称"></el-input>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="editForm.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
                    </el-form-item>
                </template>

                <!-- 子产品组编辑 -->
                <template v-if="editDialogType === 'sub_product_group'">
                    <el-form-item label="制造商" prop="manufacturer_id">
                        <el-select v-model="editForm.manufacturer_id" placeholder="请选择制造商" style="width: 100%;" @change="onEditManufacturerChange">
                            <el-option
                                v-for="mfg in allManufacturers"
                                :key="mfg.id"
                                :label="mfg.name"
                                :value="mfg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="产品组" prop="product_group_id">
                        <el-select v-model="editForm.product_group_id" placeholder="请选择产品组" style="width: 100%;">
                            <el-option
                                v-for="pg in editFilteredProductGroups"
                                :key="pg.id"
                                :label="pg.name"
                                :value="pg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="子产品组名称" prop="name">
                        <el-input v-model="editForm.name" placeholder="请输入子产品组名称"></el-input>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="editForm.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
                    </el-form-item>
                </template>

                <!-- 元器件编辑 -->
                <template v-if="editDialogType === 'component'">
                    <el-form-item label="制造商" prop="manufacturer_id">
                        <el-select v-model="editForm.manufacturer_id" placeholder="请选择制造商" style="width: 100%;" @change="onEditComponentManufacturerChange">
                            <el-option
                                v-for="mfg in allManufacturers"
                                :key="mfg.id"
                                :label="mfg.name"
                                :value="mfg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="产品组" prop="product_group_id">
                        <el-select v-model="editForm.product_group_id" placeholder="请选择产品组" style="width: 100%;" @change="onEditComponentProductGroupChange">
                            <el-option
                                v-for="pg in editFilteredProductGroups"
                                :key="pg.id"
                                :label="pg.name"
                                :value="pg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="子产品组" prop="sub_product_group_id">
                        <el-select v-model="editForm.sub_product_group_id" placeholder="请选择子产品组" style="width: 100%;">
                            <el-option
                                v-for="spg in editFilteredSubProductGroups"
                                :key="spg.id"
                                :label="spg.name"
                                :value="spg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="型号" prop="model">
                        <el-input v-model="editForm.model" placeholder="请输入型号"></el-input>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="editForm.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
                    </el-form-item>
                    <el-form-item label="备注" prop="notes">
                        <el-input v-model="editForm.notes" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
                    </el-form-item>
                </template>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="editDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitEdit">确定</el-button>
            </div>
        </el-dialog>

        <!-- 添加对话框 -->
        <el-dialog :title="addDialogTitle" :visible.sync="addDialogVisible" width="600px" class="edit-dialog">
            <el-form :model="addForm" :rules="addFormRules" ref="addForm" label-width="120px">
                <!-- 制造商添加 -->
                <template v-if="addDialogType === 'manufacturer'">
                    <el-form-item label="制造商名称" prop="name">
                        <el-input v-model="addForm.name" placeholder="请输入制造商名称"></el-input>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="addForm.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
                    </el-form-item>
                </template>

                <!-- 产品组添加 -->
                <template v-if="addDialogType === 'product_group'">
                    <el-form-item label="制造商" prop="manufacturer_id">
                        <el-select v-model="addForm.manufacturer_id" placeholder="请选择制造商" style="width: 100%;">
                            <el-option
                                v-for="mfg in allManufacturers"
                                :key="mfg.id"
                                :label="mfg.name"
                                :value="mfg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="产品组名称" prop="name">
                        <el-input v-model="addForm.name" placeholder="请输入产品组名称"></el-input>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="addForm.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
                    </el-form-item>
                </template>

                <!-- 子产品组添加 -->
                <template v-if="addDialogType === 'sub_product_group'">
                    <el-form-item label="制造商" prop="manufacturer_id">
                        <el-select v-model="addForm.manufacturer_id" placeholder="请选择制造商" style="width: 100%;" @change="onAddManufacturerChange">
                            <el-option
                                v-for="mfg in allManufacturers"
                                :key="mfg.id"
                                :label="mfg.name"
                                :value="mfg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="产品组" prop="product_group_id">
                        <el-select v-model="addForm.product_group_id" placeholder="请选择产品组" style="width: 100%;">
                            <el-option
                                v-for="pg in addFilteredProductGroups"
                                :key="pg.id"
                                :label="pg.name"
                                :value="pg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="子产品组名称" prop="name">
                        <el-input v-model="addForm.name" placeholder="请输入子产品组名称"></el-input>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="addForm.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
                    </el-form-item>
                </template>

                <!-- 元器件添加 -->
                <template v-if="addDialogType === 'component'">
                    <el-form-item label="制造商" prop="manufacturer_id">
                        <el-select v-model="addForm.manufacturer_id" placeholder="请选择制造商" style="width: 100%;" @change="onAddComponentManufacturerChange">
                            <el-option
                                v-for="mfg in allManufacturers"
                                :key="mfg.id"
                                :label="mfg.name"
                                :value="mfg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="产品组" prop="product_group_id">
                        <el-select v-model="addForm.product_group_id" placeholder="请选择产品组" style="width: 100%;" @change="onAddComponentProductGroupChange">
                            <el-option
                                v-for="pg in addFilteredProductGroups"
                                :key="pg.id"
                                :label="pg.name"
                                :value="pg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="子产品组" prop="sub_product_group_id">
                        <el-select v-model="addForm.sub_product_group_id" placeholder="请选择子产品组" style="width: 100%;">
                            <el-option
                                v-for="spg in addFilteredSubProductGroups"
                                :key="spg.id"
                                :label="spg.name"
                                :value="spg.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="型号" prop="model">
                        <el-input v-model="addForm.model" placeholder="请输入型号"></el-input>
                    </el-form-item>
                    <el-form-item label="描述" prop="description">
                        <el-input v-model="addForm.description" type="textarea" :rows="3" placeholder="请输入描述"></el-input>
                    </el-form-item>
                    <el-form-item label="备注" prop="notes">
                        <el-input v-model="addForm.notes" type="textarea" :rows="3" placeholder="请输入备注"></el-input>
                    </el-form-item>
                </template>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="addDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitAdd">确定</el-button>
            </div>
        </el-dialog>
    </div>

    <script src="lib/vue.min.js"></script>
    <script src="lib/element-ui.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
